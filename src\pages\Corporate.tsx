import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';

const Corporate = () => {
  return (
    <div className="min-h-screen">
      <Navigation />
      <div className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Corporate
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Leading MEP construction company with years of expertise in mechanical, electrical, and plumbing installations
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl font-bold text-primary">About Nile Pro</h2>
              <p className="text-lg text-muted-foreground">
                Nile Pro for Construction specializes in MEP Installation Works, providing comprehensive mechanical, electrical, and plumbing solutions for commercial and industrial projects.
              </p>
              <p className="text-lg text-muted-foreground">
                With our experienced team and cutting-edge technology, we deliver high-quality installations that meet international standards and exceed client expectations.
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-8">
                <div className="text-center p-6 bg-card rounded-lg border">
                  <h3 className="text-2xl font-bold text-primary mb-2">15+</h3>
                  <p className="text-muted-foreground">Years Experience</p>
                </div>
                <div className="text-center p-6 bg-card rounded-lg border">
                  <h3 className="text-2xl font-bold text-primary mb-2">500+</h3>
                  <p className="text-muted-foreground">Projects Completed</p>
                </div>
              </div>
            </div>
            <div className="aspect-video rounded-lg overflow-hidden shadow-elegant">
              <img
                src="https://images.unsplash.com/photo-1487958449943-2429e8be8625?w=600&h=400&fit=crop"
                alt="Modern building"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default Corporate;