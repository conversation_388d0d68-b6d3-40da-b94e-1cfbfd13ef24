import { useState } from 'react';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { getAllProducts } from '@/utils/productUtils';


const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);

  const products = getAllProducts();

  const navItems = [
    { name: 'Home', href: '/' },
    { name: 'Corporate', href: '/corporate' },
    {
      name: 'Products',
      href: '/products',
      submenu: products.map(product => ({
        name: product.title,
        href: `/products/${product.slug}`
      }))
    },
    {
      name: 'Solutions',
      href: '/solutions',
      submenu: [
        { name: 'Hospitality', href: '/solutions/hospitality' },
        { name: 'Healthcare', href: '/solutions/healthcare' },
        { name: 'Pharmaceutical', href: '/solutions/pharmaceutical' },
        { name: 'Business', href: '/solutions/business' },
        { name: 'Food and Beverage', href: '/solutions/food-beverage' },
        { name: 'Commercial', href: '/solutions/commercial' }
      ]
    },
    { name: 'References', href: '/references' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <img
              src="/logo.png"
              alt="Nile Pro Logo"
              className="h-12 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => (
              <div key={item.name} className="relative group">
                {item.submenu ? (
                  <div className="relative">
                    <button
                      className="flex items-center text-foreground hover:text-primary transition-colors duration-200 font-medium"
                      onMouseEnter={() => setActiveDropdown(item.name)}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      {item.name}
                      <ChevronDown className="ml-1 h-4 w-4" />
                    </button>
                    {activeDropdown === item.name && (
                      <div
                        className="absolute top-full left-0 mt-2 w-64 bg-background border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
                        onMouseEnter={() => setActiveDropdown(item.name)}
                        onMouseLeave={() => setActiveDropdown(null)}
                      >
                        {item.name === 'Products' && (
                          <div className="px-4 py-2 border-b border-border">
                            <Link
                              to="/products"
                              className="text-sm font-medium text-primary hover:text-primary-dark transition-colors"
                            >
                              View All Products
                            </Link>
                          </div>
                        )}
                        {item.submenu.map((subItem) => (
                          <Link
                            key={typeof subItem === 'string' ? subItem : subItem.name}
                            to={typeof subItem === 'string' ? item.href : subItem.href}
                            className="block px-4 py-2 text-sm text-muted-foreground hover:text-primary hover:bg-muted transition-colors"
                          >
                            {typeof subItem === 'string' ? subItem : subItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.href}
                    className="text-foreground hover:text-primary transition-colors duration-200 font-medium"
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
            <Button className="bg-gradient-primary hover:opacity-90 shadow-primary">
              Get Quote
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2"
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border bg-background">
            <div className="flex flex-col space-y-3">
              {navItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="text-foreground hover:text-primary transition-colors duration-200 font-medium px-2 py-1"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <Button className="bg-gradient-primary hover:opacity-90 shadow-primary mt-4 mx-2">
                Get Quote
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;