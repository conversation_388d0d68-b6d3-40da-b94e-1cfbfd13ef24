import { useState } from 'react';
import { Menu, X, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { getAllProducts } from '@/utils/productUtils';


const Navigation = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);
  const [activeMobileSubmenu, setActiveMobileSubmenu] = useState(null);

  const products = getAllProducts();

  const navItems = [
    { name: 'Home', href: '/' },
    { name: 'Corporate', href: '/corporate' },
    {
      name: 'Products',
      href: '/products',
      submenu: products.map(product => ({
        name: product.title,
        href: `/products/${product.slug}`
      }))
    },
    {
      name: 'Solutions',
      href: '/solutions',
      submenu: [
        { name: 'Hospitality', href: '/solutions/hospitality' },
        { name: 'Healthcare', href: '/solutions/healthcare' },
        { name: 'Pharmaceutical', href: '/solutions/pharmaceutical' },
        { name: 'Business', href: '/solutions/business' },
        { name: 'Food and Beverage', href: '/solutions/food-beverage' },
        { name: 'Commercial', href: '/solutions/commercial' }
      ]
    },
    { name: 'References', href: '/references' },
    { name: 'Contact', href: '/contact' },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3">
            <img
              src="/logo.png"
              alt="Nile Pro Logo"
              className="h-12 w-auto"
            />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navItems.map((item) => (
              <div key={item.name} className="relative group">
                {item.submenu ? (
                  <div className="relative">
                    <button
                      className="flex items-center text-foreground hover:text-primary transition-colors duration-200 font-medium"
                      onMouseEnter={() => setActiveDropdown(item.name)}
                      onMouseLeave={() => setActiveDropdown(null)}
                    >
                      {item.name}
                      <ChevronDown className="ml-1 h-4 w-4" />
                    </button>
                    {activeDropdown === item.name && (
                      <div
                        className="absolute top-full left-0 mt-2 w-64 bg-background border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto"
                        onMouseEnter={() => setActiveDropdown(item.name)}
                        onMouseLeave={() => setActiveDropdown(null)}
                      >
                        {item.name === 'Products' && (
                          <div className="px-4 py-2 border-b border-border">
                            <Link
                              to="/products"
                              className="text-sm font-medium text-primary hover:text-primary-dark transition-colors"
                            >
                              View All Products
                            </Link>
                          </div>
                        )}
                        {item.submenu.map((subItem) => (
                          <Link
                            key={typeof subItem === 'string' ? subItem : subItem.name}
                            to={typeof subItem === 'string' ? item.href : subItem.href}
                            className="block px-4 py-2 text-sm text-muted-foreground hover:text-primary hover:bg-muted transition-colors"
                          >
                            {typeof subItem === 'string' ? subItem : subItem.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    to={item.href}
                    className="text-foreground hover:text-primary transition-colors duration-200 font-medium"
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
            <Button className="bg-gradient-primary hover:opacity-90 shadow-primary">
              Get Quote
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-3 min-h-[44px] min-w-[44px]"
              aria-label="Toggle navigation menu"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border bg-background">
            <div className="flex flex-col space-y-2">
              {navItems.map((item) => (
                <div key={item.name}>
                  {item.submenu ? (
                    <div>
                      <button
                        className="w-full flex items-center justify-between text-foreground hover:text-primary transition-colors duration-200 font-medium px-4 py-3 min-h-[44px]"
                        onClick={() => setActiveMobileSubmenu(activeMobileSubmenu === item.name ? null : item.name)}
                        aria-expanded={activeMobileSubmenu === item.name}
                      >
                        {item.name}
                        <ChevronDown className={`h-4 w-4 transition-transform ${activeMobileSubmenu === item.name ? 'rotate-180' : ''}`} />
                      </button>
                      {activeMobileSubmenu === item.name && (
                        <div className="bg-muted/50 py-2">
                          {item.name === 'Products' && (
                            <Link
                              to="/products"
                              className="block px-6 py-2 text-sm font-medium text-primary hover:text-primary-dark transition-colors min-h-[44px] flex items-center"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              View All Products
                            </Link>
                          )}
                          {item.submenu.map((subItem) => (
                            <Link
                              key={typeof subItem === 'string' ? subItem : subItem.name}
                              to={typeof subItem === 'string' ? item.href : subItem.href}
                              className="block px-6 py-2 text-sm text-muted-foreground hover:text-primary transition-colors min-h-[44px] flex items-center"
                              onClick={() => setIsMenuOpen(false)}
                            >
                              {typeof subItem === 'string' ? subItem : subItem.name}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.href}
                      className="block text-foreground hover:text-primary transition-colors duration-200 font-medium px-4 py-3 min-h-[44px] flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )}
                </div>
              ))}
              <div className="px-4 pt-2">
                <Button className="w-full bg-gradient-primary hover:opacity-90 shadow-primary min-h-[44px]">
                  Get Quote
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;