import { CheckCircle, Target, Eye, Lightbulb } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

const About = () => {
  const features = [
    {
      icon: <Target className="h-6 w-6" />,
      title: "Precision Engineering",
      description: "Every project is executed with meticulous attention to detail and engineering excellence."
    },
    {
      icon: <Eye className="h-6 w-6" />,
      title: "Quality Assurance",
      description: "Rigorous quality control processes ensure the highest standards in all our installations."
    },
    {
      icon: <Lightbulb className="h-6 w-6" />,
      title: "Innovative Solutions",
      description: "We leverage cutting-edge technology to deliver innovative MEP solutions for modern buildings."
    }
  ];

  const achievements = [
    "15+ years of industry expertise",
    "500+ successful project completions",
    "Certified MEP installation specialists",
    "24/7 maintenance and support services",
    "Energy-efficient system designs",
    "Compliance with international standards"
  ];

  return (
    <section id="about" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            About <span className="text-primary">Nile Pro</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Leading MEP construction specialists dedicated to delivering exceptional
            mechanical, electrical, and plumbing solutions for commercial and industrial projects.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center mb-16">
          {/* Left Content */}
          <div className="space-y-6">
            <h3 className="text-2xl md:text-3xl font-bold text-foreground">
              Engineering Excellence Since 2009
            </h3>

            <p className="text-lg text-muted-foreground leading-relaxed">
              Nile Pro for Construction has established itself as a premier MEP installation contractor,
              combining advanced technology with durable design principles. Our comprehensive approach
              ensures optimal comfort, efficiency, and performance in every environment we serve.
            </p>

            <p className="text-lg text-muted-foreground leading-relaxed">
              From complex commercial buildings to industrial facilities, we deliver integrated
              MEP solutions that exceed expectations while maintaining the highest safety and
              quality standards.
            </p>

            {/* Achievements List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-8">
              {achievements.map((achievement, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-primary flex-shrink-0" />
                  <span className="text-muted-foreground">{achievement}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content - Feature Cards */}
          <div className="space-y-6">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-primary transition-all duration-300 border-border">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="p-3 bg-primary/10 rounded-lg">
                      <div className="text-primary">
                        {feature.icon}
                      </div>
                    </div>
                    <div>
                      <h4 className="text-xl font-semibold text-foreground mb-2">
                        {feature.title}
                      </h4>
                      <p className="text-muted-foreground">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Mission & Vision */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="border-primary/20 hover:shadow-primary transition-all duration-300">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Our Mission</h3>
              <p className="text-muted-foreground leading-relaxed">
                To deliver exceptional MEP solutions that combine innovation, quality, and reliability,
                ensuring our clients achieve optimal building performance and energy efficiency.
              </p>
            </CardContent>
          </Card>

          <Card className="border-accent/20 hover:shadow-large transition-all duration-300">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6">
                <Eye className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-foreground mb-4">Our Vision</h3>
              <p className="text-muted-foreground leading-relaxed">
                To be the leading MEP contractor recognized for engineering excellence,
                innovative solutions, and unwavering commitment to client satisfaction.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default About;