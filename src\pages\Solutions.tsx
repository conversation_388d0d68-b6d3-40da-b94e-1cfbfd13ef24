import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Building2, 
  Heart, 
  FlaskConical, 
  Briefcase, 
  UtensilsCrossed, 
  Store,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

const Solutions = () => {
  const solutions = [
    {
      icon: <Building2 className="h-12 w-12" />,
      title: "Hospitality",
      description: "Complete MEP solutions for hotels, resorts, and hospitality venues with focus on guest comfort and energy efficiency.",
      features: ["Guest Room Climate Control", "Central HVAC Systems", "Kitchen Ventilation", "Pool & Spa Systems", "Emergency Systems"],
      projects: "50+ Hotels",
      href: "/solutions/hospitality"
    },
    {
      icon: <Heart className="h-12 w-12" />,
      title: "Healthcare",
      description: "Specialized MEP installations for hospitals, clinics, and medical facilities meeting strict health and safety standards.",
      features: ["Medical Gas Systems", "Clean Room HVAC", "Emergency Power", "Infection Control", "Operating Room Systems"],
      projects: "25+ Medical Facilities",
      href: "/solutions/healthcare"
    },
    {
      icon: <FlaskConical className="h-12 w-12" />,
      title: "Pharmaceutical",
      description: "Precision MEP systems for pharmaceutical manufacturing with controlled environments and regulatory compliance.",
      features: ["Clean Room Design", "Process Cooling", "Compressed Air Systems", "Waste Management", "Validation Support"],
      projects: "15+ Pharma Plants",
      href: "/solutions/pharmaceutical"
    },
    {
      icon: <Briefcase className="h-12 w-12" />,
      title: "Business",
      description: "Modern MEP solutions for office buildings, corporate headquarters, and business complexes.",
      features: ["Smart Building Systems", "Energy Management", "Conference Room AV", "Security Integration", "Flexible Workspaces"],
      projects: "100+ Office Buildings",
      href: "/solutions/business"
    },
    {
      icon: <UtensilsCrossed className="h-12 w-12" />,
      title: "Food and Beverage",
      description: "Specialized MEP systems for food processing, restaurants, and beverage manufacturing facilities.",
      features: ["Food Grade Systems", "Cold Storage", "Process Ventilation", "Waste Water Treatment", "Hygiene Systems"],
      projects: "30+ F&B Facilities",
      href: "/solutions/food-beverage"
    },
    {
      icon: <Store className="h-12 w-12" />,
      title: "Commercial",
      description: "Comprehensive MEP solutions for retail spaces, shopping centers, and commercial developments.",
      features: ["Retail HVAC", "Lighting Design", "Fire Safety", "Escalator Systems", "Parking Ventilation"],
      projects: "75+ Commercial Projects",
      href: "/solutions/commercial"
    }
  ];

  const benefits = [
    "Industry-specific expertise and compliance",
    "Energy-efficient and sustainable solutions",
    "24/7 maintenance and support services",
    "Advanced building automation systems",
    "Regulatory compliance and certifications",
    "Cost-effective lifecycle management"
  ];

  return (
    <div className="min-h-screen">
      <Navigation />
      
      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Industry Solutions
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Specialized MEP solutions tailored to meet the unique requirements of different industries, 
              ensuring optimal performance, compliance, and efficiency.
            </p>
          </div>
        </div>
      </section>

      {/* Solutions Grid */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {solutions.map((solution, index) => (
              <Card key={index} className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] border-border">
                <CardHeader className="text-center pb-4">
                  <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <div className="text-white">
                      {solution.icon}
                    </div>
                  </div>
                  <CardTitle className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
                    {solution.title}
                  </CardTitle>
                  <Badge variant="secondary" className="w-fit mx-auto">
                    {solution.projects}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground text-center">
                    {solution.description}
                  </p>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-foreground">Key Features:</h4>
                    <ul className="space-y-1">
                      {solution.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start text-sm text-muted-foreground">
                          <CheckCircle className="h-4 w-4 text-primary mr-2 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Button 
                    className="w-full bg-gradient-primary hover:opacity-90 shadow-primary group"
                    onClick={() => window.location.href = solution.href}
                  >
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Why Choose Our <span className="text-primary">Solutions</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Our industry-specific approach ensures that every MEP system is designed and implemented 
              to meet the unique challenges and requirements of your sector.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center space-x-3 p-4 bg-background rounded-lg border border-border">
                <CheckCircle className="h-6 w-6 text-primary flex-shrink-0" />
                <span className="text-foreground font-medium">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Solutions;
