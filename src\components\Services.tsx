import {
  Zap,
  Droplets,
  Wind,
  Shield,
  Settings,
  Wrench,
  ThermometerSun,
  Activity
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Services = () => {
  const services = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Electrical Systems",
      description: "Complete electrical installation, power distribution, lighting systems, and emergency backup solutions.",
      features: ["Power Distribution", "Lighting Design", "Emergency Systems", "Smart Controls"]
    },
    {
      icon: <Droplets className="h-8 w-8" />,
      title: "Plumbing Systems",
      description: "Comprehensive plumbing solutions including water supply, drainage, and specialized piping systems.",
      features: ["Water Supply", "Drainage Systems", "Fire Protection", "Medical Gas"]
    },
    {
      icon: <Wind className="h-8 w-8" />,
      title: "HVAC Systems",
      description: "Advanced heating, ventilation, and air conditioning systems for optimal indoor climate control.",
      features: ["Climate Control", "Ventilation", "Air Quality", "Energy Recovery"]
    },
    {
      icon: <ThermometerSun className="h-8 w-8" />,
      title: "Thermal Systems",
      description: "Efficient heating and cooling solutions including boilers, chillers, and heat pumps.",
      features: ["Boiler Systems", "Chiller Plants", "Heat Pumps", "Thermal Storage"]
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Fire Protection",
      description: "Comprehensive fire safety systems including detection, suppression, and emergency response.",
      features: ["Fire Detection", "Sprinkler Systems", "Suppression", "Emergency Egress"]
    },
    {
      icon: <Activity className="h-8 w-8" />,
      title: "Building Automation",
      description: "Smart building management systems for optimal performance and energy efficiency.",
      features: ["BMS Integration", "Energy Management", "Remote Monitoring", "Predictive Maintenance"]
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: "System Integration",
      description: "Seamless integration of all MEP systems for coordinated and efficient building operations.",
      features: ["System Coordination", "Performance Optimization", "Interface Management", "Testing & Commissioning"]
    },
    {
      icon: <Wrench className="h-8 w-8" />,
      title: "Maintenance Services",
      description: "Comprehensive maintenance and support services to ensure long-term system reliability.",
      features: ["Preventive Maintenance", "24/7 Support", "System Upgrades", "Performance Monitoring"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-muted/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Our <span className="text-primary">Services</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive MEP solutions designed to meet the complex demands of modern construction projects
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {services.map((service, index) => (
            <Card
              key={index}
              className="group hover:shadow-primary transition-all duration-300 border-border hover:border-primary/50 bg-background"
            >
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <div className="text-white">
                    {service.icon}
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                  {service.title}
                </CardTitle>
              </CardHeader>

              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {service.description}
                </p>

                <ul className="space-y-2 text-sm">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="text-muted-foreground flex items-center justify-center">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>


      </div>
    </section>
  );
};

export default Services;