import {
  Zap,
  Droplets,
  Wind,
  Shield,
  Settings,
  Wrench,
  ThermometerSun,
  Activity,
  Phone,
  Download,
  CheckCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Services = () => {
  const services = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Electrical Systems",
      description: "Complete electrical installation, power distribution, lighting systems, and emergency backup solutions.",
      features: ["Power Distribution", "Lighting Design", "Emergency Systems", "Smart Controls"]
    },
    {
      icon: <Droplets className="h-8 w-8" />,
      title: "Plumbing Systems",
      description: "Comprehensive plumbing solutions including water supply, drainage, and specialized piping systems.",
      features: ["Water Supply", "Drainage Systems", "Fire Protection", "Medical Gas"]
    },
    {
      icon: <Wind className="h-8 w-8" />,
      title: "HVAC Systems",
      description: "Advanced heating, ventilation, and air conditioning systems for optimal indoor climate control.",
      features: ["Climate Control", "Ventilation", "Air Quality", "Energy Recovery"]
    },
    {
      icon: <ThermometerSun className="h-8 w-8" />,
      title: "Thermal Systems",
      description: "Efficient heating and cooling solutions including boilers, chillers, and heat pumps.",
      features: ["Boiler Systems", "Chiller Plants", "Heat Pumps", "Thermal Storage"]
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Fire Protection",
      description: "Comprehensive fire safety systems including detection, suppression, and emergency response.",
      features: ["Fire Detection", "Sprinkler Systems", "Suppression", "Emergency Egress"]
    },
    {
      icon: <Activity className="h-8 w-8" />,
      title: "Building Automation",
      description: "Smart building management systems for optimal performance and energy efficiency.",
      features: ["BMS Integration", "Energy Management", "Remote Monitoring", "Predictive Maintenance"]
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: "System Integration",
      description: "Seamless integration of all MEP systems for coordinated and efficient building operations.",
      features: ["System Coordination", "Performance Optimization", "Interface Management", "Testing & Commissioning"]
    },
    {
      icon: <Wrench className="h-8 w-8" />,
      title: "Maintenance Services",
      description: "Comprehensive maintenance and support services to ensure long-term system reliability.",
      features: ["Preventive Maintenance", "24/7 Support", "System Upgrades", "Performance Monitoring"]
    }
  ];

  return (
    <section id="services" className="py-20 bg-muted/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-foreground mb-6">
            Our <span className="text-primary">Services</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Comprehensive MEP solutions designed to meet the complex demands of modern construction projects
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {services.map((service, index) => (
            <Card
              key={index}
              className="group hover:shadow-primary transition-all duration-300 border-border hover:border-primary/50 bg-background"
            >
              <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <div className="text-white">
                    {service.icon}
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                  {service.title}
                </CardTitle>
              </CardHeader>

              <CardContent className="text-center">
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {service.description}
                </p>

                <ul className="space-y-2 text-sm">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="text-muted-foreground flex items-center justify-center">
                      <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2 flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action Section */}
        <div className="mt-20">
          <div className="bg-gradient-to-r from-primary/10 via-accent/10 to-primary/10 rounded-2xl p-8 md:p-12 text-center border border-primary/20">
            <div className="max-w-4xl mx-auto">
              <h3 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 bg-gradient-primary bg-clip-text text-transparent">
                Ready to Transform Your Building's MEP Systems?
              </h3>
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                Let our expert engineers design and implement cutting-edge MEP solutions tailored to your specific needs.
                From concept to completion, we deliver excellence in every project.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <Button size="lg" className="w-full sm:w-auto bg-gradient-primary hover:opacity-90 shadow-primary min-h-[44px] px-8">
                  <Phone className="mr-2 h-5 w-5" />
                  Get Free Consultation
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="w-full sm:w-auto border-2 border-primary text-primary hover:bg-primary hover:text-white min-h-[44px] px-8"
                >
                  <Download className="mr-2 h-5 w-5" />
                  Download Brochure
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-8 border-t border-border/50">
                <div className="flex items-center justify-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <CheckCircle className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium text-foreground">15+ Years Experience</span>
                </div>
                <div className="flex items-center justify-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <CheckCircle className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium text-foreground">500+ Projects Completed</span>
                </div>
                <div className="flex items-center justify-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-full">
                    <CheckCircle className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-sm font-medium text-foreground">24/7 Support Available</span>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </section>
  );
};

export default Services;