import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Building2,
  MapPin,
  Calendar,
  Users,
  Award,
  Star,
  ExternalLink
} from 'lucide-react';

const References = () => {
  const projects = [
    {
      title: "Grand Plaza Hotel",
      location: "Cairo, Egypt",
      category: "Hospitality",
      year: "2023",
      description: "Complete MEP installation for 300-room luxury hotel including HVAC, electrical, plumbing, and fire safety systems.",
      scope: "Full MEP Installation",
      value: "$2.5M",
      image: "/placeholder.svg",
      features: ["Central HVAC System", "Guest Room Controls", "Kitchen Ventilation", "Pool Systems"]
    },
    {
      title: "Cairo Medical Center",
      location: "New Cairo, Egypt",
      category: "Healthcare",
      year: "2023",
      description: "Specialized MEP systems for 200-bed hospital including medical gas, clean rooms, and emergency power systems.",
      scope: "Medical MEP Systems",
      value: "$3.2M",
      image: "/placeholder.svg",
      features: ["Medical Gas Systems", "Operating Room HVAC", "Emergency Power", "Infection Control"]
    },
    {
      title: "Pharma Manufacturing Plant",
      location: "6th of October City, Egypt",
      category: "Pharmaceutical",
      year: "2022",
      description: "Clean room MEP systems for pharmaceutical manufacturing facility with strict environmental controls.",
      scope: "Clean Room Systems",
      value: "$4.1M",
      image: "/placeholder.svg",
      features: ["Clean Room HVAC", "Process Cooling", "Compressed Air", "Validation Support"]
    },
    {
      title: "Business Tower Complex",
      location: "New Administrative Capital, Egypt",
      category: "Commercial",
      year: "2022",
      description: "Smart building MEP systems for 40-story office tower with advanced automation and energy management.",
      scope: "Smart Building MEP",
      value: "$5.8M",
      image: "/placeholder.svg",
      features: ["Building Automation", "Energy Management", "Smart Lighting", "Security Integration"]
    },
    {
      title: "Food Processing Facility",
      location: "Alexandria, Egypt",
      category: "Food & Beverage",
      year: "2021",
      description: "Specialized MEP systems for food processing plant including cold storage and hygiene systems.",
      scope: "Food Grade Systems",
      value: "$1.8M",
      image: "/placeholder.svg",
      features: ["Cold Storage Systems", "Process Ventilation", "Hygiene Systems", "Waste Water Treatment"]
    },
    {
      title: "Shopping Mall Complex",
      location: "Giza, Egypt",
      category: "Retail",
      year: "2021",
      description: "Complete MEP installation for large shopping center including retail spaces, restaurants, and entertainment areas.",
      scope: "Retail MEP Systems",
      value: "$3.7M",
      image: "/placeholder.svg",
      features: ["Retail HVAC", "Escalator Systems", "Fire Safety", "Parking Ventilation"]
    }
  ];

  const stats = [
    { icon: <Building2 className="h-8 w-8" />, value: "200+", label: "Projects Completed" },
    { icon: <Users className="h-8 w-8" />, value: "150+", label: "Satisfied Clients" },
    { icon: <Award className="h-8 w-8" />, value: "15+", label: "Years Experience" },
    { icon: <Star className="h-8 w-8" />, value: "98%", label: "Client Satisfaction" }
  ];

  const testimonials = [
    {
      name: "Ahmed Hassan",
      position: "Project Manager",
      company: "Grand Plaza Hotel",
      quote: "Nile Pro delivered exceptional MEP solutions for our hotel. Their attention to detail and professional approach exceeded our expectations.",
      rating: 5
    },
    {
      name: "Dr. Sarah Mohamed",
      position: "Facility Director",
      company: "Cairo Medical Center",
      quote: "The medical MEP systems installed by Nile Pro meet all international standards. Their expertise in healthcare facilities is outstanding.",
      rating: 5
    },
    {
      name: "Mahmoud Ali",
      position: "Operations Manager",
      company: "Business Tower Complex",
      quote: "The smart building systems implemented by Nile Pro have significantly improved our energy efficiency and operational costs.",
      rating: 5
    }
  ];

  return (
    <div className="min-h-screen">
      <Navigation />

      {/* Hero Section */}
      <section className="pt-20 pb-16 bg-gradient-to-br from-primary/5 to-accent/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-primary bg-clip-text text-transparent">
              Our References
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Explore our portfolio of successful MEP projects across various industries.
              Each project showcases our commitment to excellence and innovation.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
            {stats.map((stat, index) => (
              <Card key={index} className="text-center border-border">
                <CardContent className="p-6">
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-white">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-primary mb-2">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20 bg-background">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Featured <span className="text-primary">Projects</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              A selection of our most significant MEP projects demonstrating our expertise across different sectors.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {projects.map((project, index) => (
              <Card key={index} className="group hover:shadow-elegant transition-all duration-300 hover:scale-[1.02] border-border">
                <div className="aspect-video overflow-hidden rounded-t-lg">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="secondary">{project.category}</Badge>
                    <span className="text-sm text-muted-foreground flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {project.year}
                    </span>
                  </div>
                  <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors">
                    {project.title}
                  </CardTitle>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    {project.location}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground text-sm">
                    {project.description}
                  </p>
                  <div className="flex justify-between text-sm">
                    <span className="text-foreground font-medium">Scope: {project.scope}</span>
                    <span className="text-primary font-bold">{project.value}</span>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-foreground text-sm">Key Features:</h4>
                    <div className="flex flex-wrap gap-1">
                      {project.features.map((feature, featureIndex) => (
                        <Badge key={featureIndex} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <Button variant="outline" className="w-full group border-primary text-primary hover:bg-primary hover:text-white min-h-[44px]">
                    View Details
                    <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-muted/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Client <span className="text-primary">Testimonials</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Hear what our clients say about our MEP solutions and services.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-border">
                <CardContent className="p-6">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-muted-foreground mb-4 italic">
                    "{testimonial.quote}"
                  </p>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.position}</div>
                    <div className="text-sm text-primary">{testimonial.company}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default References;
